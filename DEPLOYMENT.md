# 🚀 FraudGuard 360° - Deployment Guide

## 📋 **Deployment Modes**

The platform supports two deployment modes:

### **🎮 Demo Mode (Mock Data)**
- ✅ No database required
- ✅ Uses intelligent mock data generation
- ✅ Perfect for showcasing and development
- ✅ Zero infrastructure costs
- ✅ Instant setup

### **🏗️ Production Mode (Real Database)**
- 🗄️ PostgreSQL database required
- 🔒 Real JWT authentication
- 📊 Persistent data storage
- 🚀 Scalable architecture
- 📈 Production-ready features

---

## 🚀 **Deployment Options**

### **Option 1: Local Development (Demo Mode)**
```bash
# Automated setup
npm install --legacy-peer-deps
npm run setup
# Choose option 1 for Demo Mode

# Start development server
npm run dev
# Access at http://localhost:3000
```

### **Option 2: Local Development (Production Mode)**
```bash
# Install dependencies
npm install --legacy-peer-deps

# Set up PostgreSQL database
createdb fraudguard_dev

# Configure environment
cp .env.example .env.local
# Edit DATABASE_URL in .env.local

# Set up database
npm run db:generate
npm run db:push
npm run db:seed

# Start development
npm run dev
```

### **Option 3: Docker Compose (Full Stack)**
```bash
# Deploy complete stack locally
docker-compose up -d

# Access points:
# - Application: http://localhost:3000
# - Grafana Dashboard: http://localhost:3001 (admin/admin123)
# - Prometheus: http://localhost:9090
# - Kibana: http://localhost:5601

# View logs
docker-compose logs -f fraudguard-app

# Stop stack
docker-compose down
```

### **Option 4: Kubernetes (Production)**
```bash
# Deploy to Kubernetes cluster
./scripts/deploy.sh production -v latest

# Or deploy to staging
./scripts/deploy.sh staging -v v1.2.3

# Check deployment status
kubectl get pods -n fraudguard
kubectl get services -n fraudguard

# View logs
kubectl logs -f deployment/fraudguard-app -n fraudguard
```

### **Option 5: Automated Deployment Script**
```bash
# Make script executable
chmod +x scripts/deploy.sh

# Local deployment
./scripts/deploy.sh local

# Staging deployment
./scripts/deploy.sh staging -v v1.2.3

# Production deployment
./scripts/deploy.sh production -v latest

# Dry run (see what would be deployed)
./scripts/deploy.sh production --dry-run
```

### **Option 2: Docker Compose (Recommended for Demo)**
```bash
# Build and run with all services
docker-compose up -d

# Access the application
# - App: http://localhost:3000
# - Grafana: http://localhost:3001
# - Prometheus: http://localhost:9090
```

### **Option 3: Kubernetes (Production)**
```bash
# Deploy to Kubernetes cluster
kubectl apply -f k8s/
kubectl get pods -l app=fraudguard
```

---

## 🐳 **Docker Deployment**

### **Single Container**
```bash
# Build the image
docker build -t fraudguard-360 .

# Run the container
docker run -p 3000:3000 fraudguard-360
```

### **Multi-Service with Docker Compose**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f fraudguard-app

# Stop services
docker-compose down
```

**Services included:**
- **FraudGuard App** (Port 3000)
- **PostgreSQL** (Port 5432)
- **Redis** (Port 6379)
- **Nginx** (Ports 80/443)
- **Prometheus** (Port 9090)
- **Grafana** (Port 3001)

---

## ☸️ **Kubernetes Deployment**

### **Prerequisites**
- Kubernetes cluster (v1.20+)
- kubectl configured
- Ingress controller (nginx)
- cert-manager (for SSL)

### **Deployment Steps**
```bash
# 1. Create namespace
kubectl create namespace fraudguard

# 2. Apply configurations
kubectl apply -f k8s/ -n fraudguard

# 3. Check deployment status
kubectl get all -n fraudguard

# 4. Get external IP
kubectl get ingress -n fraudguard
```

### **Scaling**
```bash
# Manual scaling
kubectl scale deployment fraudguard-app --replicas=5 -n fraudguard

# Auto-scaling is configured via HPA
kubectl get hpa -n fraudguard
```

---

## 🌐 **Cloud Platform Deployment**

### **AWS EKS**
```bash
# Create EKS cluster
eksctl create cluster --name fraudguard-cluster --region us-west-2

# Deploy application
kubectl apply -f k8s/

# Set up load balancer
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/aws/deploy.yaml
```

### **Google GKE**
```bash
# Create GKE cluster
gcloud container clusters create fraudguard-cluster --zone us-central1-a

# Get credentials
gcloud container clusters get-credentials fraudguard-cluster --zone us-central1-a

# Deploy application
kubectl apply -f k8s/
```

### **Azure AKS**
```bash
# Create AKS cluster
az aks create --resource-group fraudguard-rg --name fraudguard-cluster --node-count 3

# Get credentials
az aks get-credentials --resource-group fraudguard-rg --name fraudguard-cluster

# Deploy application
kubectl apply -f k8s/
```

---

## 🔧 **Environment Configuration**

### **Environment Variables**
```bash
# Production environment
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
DATABASE_URL=postgresql://user:password@host:port/db
REDIS_URL=redis://host:port
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://your-domain.com
```

### **Secrets Management**
```bash
# Create Kubernetes secrets
kubectl create secret generic fraudguard-secrets \
  --from-literal=database-url="postgresql://..." \
  --from-literal=redis-url="redis://..." \
  --from-literal=nextauth-secret="your-secret"
```

---

## 📊 **Monitoring & Observability**

### **Health Checks**
```bash
# Application health
curl http://localhost:3000/api/health

# Kubernetes health
kubectl get pods -l app=fraudguard
```

### **Metrics & Monitoring**
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **Application logs**: Structured JSON logging
- **Performance monitoring**: Real-time metrics

### **Grafana Dashboards**
1. **Application Performance**
   - Response times
   - Error rates
   - Request volume

2. **Infrastructure Metrics**
   - CPU/Memory usage
   - Network traffic
   - Database performance

3. **Business Metrics**
   - Fraud detection rates
   - User activity
   - System utilization

---

## 🔒 **Security Configuration**

### **SSL/TLS Setup**
```bash
# Install cert-manager
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.8.0/cert-manager.yaml

# Configure Let's Encrypt
kubectl apply -f k8s/cert-issuer.yaml
```

### **Network Security**
- **Ingress**: HTTPS only with automatic redirects
- **Network Policies**: Restrict pod-to-pod communication
- **Secrets**: Encrypted at rest and in transit
- **RBAC**: Role-based access control

---

## 🚀 **CI/CD Pipeline**

### **GitHub Actions**
The project includes a complete CI/CD pipeline:

1. **Code Quality**
   - TypeScript checking
   - ESLint validation
   - Prettier formatting
   - Unit tests with coverage

2. **Security Scanning**
   - npm audit
   - Trivy vulnerability scanning
   - SARIF reporting

3. **Build & Deploy**
   - Multi-platform Docker builds
   - Container registry push
   - Automated deployments

### **Pipeline Triggers**
- **Pull Requests**: Quality checks only
- **Develop Branch**: Deploy to staging
- **Main Branch**: Deploy to production

---

## 📈 **Performance Optimization**

### **Production Optimizations**
- **Next.js optimizations**: Static generation, image optimization
- **Caching**: Redis for session and data caching
- **CDN**: Static asset delivery
- **Database**: Connection pooling and indexing

### **Scaling Considerations**
- **Horizontal scaling**: Multiple app instances
- **Database scaling**: Read replicas
- **Caching**: Distributed Redis cluster
- **Load balancing**: Nginx or cloud load balancers

---

## 🔍 **Troubleshooting**

### **Common Issues**
```bash
# Check application logs
kubectl logs -l app=fraudguard -n fraudguard

# Debug pod issues
kubectl describe pod <pod-name> -n fraudguard

# Check resource usage
kubectl top pods -n fraudguard
```

### **Performance Issues**
- Monitor memory usage and CPU utilization
- Check database connection pool settings
- Verify Redis cache hit rates
- Review application metrics in Grafana

---

## 📚 **Additional Resources**

- **Documentation**: Comprehensive API and component docs
- **Monitoring**: Grafana dashboards and alerts
- **Security**: Regular security updates and patches
- **Support**: Issue tracking and resolution procedures

This deployment guide ensures your FraudGuard 360° platform can be deployed reliably across different environments while maintaining security, performance, and observability standards.
