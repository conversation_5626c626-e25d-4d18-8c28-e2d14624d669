import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

// Mock user database
const mockUsers = [
  {
    id: '1',
    username: 'fraud.analyst',
    email: '<EMAIL>',
    password: 'demo123', // In real app, this would be hashed
    role: 'fraud_analyst',
    permissions: ['view_dashboard', 'export_reports', 'manage_cases']
  },
  {
    id: '2',
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    permissions: ['view_dashboard', 'export_reports', 'manage_cases', 'admin_panel']
  },
  {
    id: '3',
    username: 'demo',
    email: '<EMAIL>',
    password: 'demo',
    role: 'demo_user',
    permissions: ['view_dashboard']
  }
]

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json()

    // Simulate authentication delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Find user
    const user = mockUsers.find(u => u.username === username && u.password === password)

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Create mock JWT token (in real app, use proper JWT library)
    const token = Buffer.from(JSON.stringify({
      userId: user.id,
      username: user.username,
      role: user.role,
      exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    })).toString('base64')

    // Set HTTP-only cookie
    const cookieStore = await cookies()
    cookieStore.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      },
      token
    })

  } catch (error) {
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    )
  }
}

// Demo credentials endpoint
export async function GET() {
  return NextResponse.json({
    demo_credentials: [
      {
        username: 'fraud.analyst',
        password: 'demo123',
        role: 'Fraud Analyst',
        description: 'Full access to fraud detection features'
      },
      {
        username: 'admin',
        password: 'admin123',
        role: 'Administrator',
        description: 'Complete system access with admin privileges'
      },
      {
        username: 'demo',
        password: 'demo',
        role: 'Demo User',
        description: 'Limited access for demonstration purposes'
      }
    ]
  })
}
