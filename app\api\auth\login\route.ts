import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { authenticateUser, getDemoCredentials, logUserAction } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json()

    // Get client IP and user agent for audit logging
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Simulate authentication delay for demo purposes
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Authenticate user (with database fallback to mock)
    const authResult = await authenticateUser(username, password)

    // Set HTTP-only cookie
    const cookieStore = await cookies()
    cookieStore.set('auth-token', authResult.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    // Log successful login
    await logUserAction(
      authResult.user.id,
      'LOGIN',
      'auth',
      { source: authResult.source },
      ipAddress,
      userAgent
    )

    return NextResponse.json({
      success: true,
      user: authResult.user,
      token: authResult.token,
      source: authResult.source // For debugging/monitoring
    })

  } catch (error) {
    console.error('Login failed:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Authentication failed',
        success: false
      },
      { status: 401 }
    )
  }
}

// Demo credentials endpoint
export async function GET() {
  return NextResponse.json({
    demo_credentials: getDemoCredentials()
  })
}
