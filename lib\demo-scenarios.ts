// Demo scenarios for showcasing different fraud patterns

export interface DemoScenario {
  id: string
  name: string
  description: string
  searchQuery: string
  searchType: "msisdn" | "imsi"
  riskLevel: "Low" | "Medium" | "High" | "Critical"
  keyFeatures: string[]
  fraudPatterns: string[]
  showcasePoints: string[]
}

export const demoScenarios: DemoScenario[] = [
  {
    id: "high-risk-international",
    name: "High-Risk International Fraud",
    description: "Subscriber with suspicious international calling patterns and device switching behavior",
    searchQuery: "+1234567890",
    searchType: "msisdn",
    riskLevel: "Critical",
    keyFeatures: [
      "Multiple IMEI usage",
      "High-risk international destinations",
      "Unusual call velocity",
      "Suspicious payment patterns"
    ],
    fraudPatterns: [
      "International Revenue Share Fraud (IRSF)",
      "Device cloning indicators",
      "Premium rate service abuse",
      "Velocity-based fraud"
    ],
    showcasePoints: [
      "AI risk score: 85+ (Critical)",
      "Cross-device correlation",
      "Geospatial analysis",
      "Real-time pattern detection"
    ]
  },
  {
    id: "bulk-sms-operation",
    name: "Bulk SMS & Spam Operation",
    description: "Commercial spam operation with high-volume messaging and tethering detection",
    searchQuery: "+1987654321",
    searchType: "msisdn",
    riskLevel: "High",
    keyFeatures: [
      "Bulk SMS detection",
      "Tethering indicators",
      "High data usage",
      "Commercial usage patterns"
    ],
    fraudPatterns: [
      "SMS spam operations",
      "Unauthorized tethering",
      "Commercial misuse",
      "Network resource abuse"
    ],
    showcasePoints: [
      "SMS pattern analysis",
      "Data usage anomalies",
      "Behavioral modeling",
      "Network abuse detection"
    ]
  },
  {
    id: "device-fraud-ring",
    name: "Device Fraud Ring",
    description: "Coordinated fraud operation using multiple stolen or cloned devices",
    searchQuery: "310150123456789",
    searchType: "imsi",
    riskLevel: "High",
    keyFeatures: [
      "Multiple device associations",
      "Rapid location changes",
      "Coordinated activities",
      "Suspicious dealer patterns"
    ],
    fraudPatterns: [
      "Device cloning",
      "SIM swapping indicators",
      "Coordinated fraud ring",
      "Identity theft"
    ],
    showcasePoints: [
      "Device fingerprinting",
      "Location correlation",
      "Network analysis",
      "Dealer intelligence"
    ]
  },
  {
    id: "normal-user-baseline",
    name: "Normal User Baseline",
    description: "Typical subscriber with normal usage patterns for comparison",
    searchQuery: "+1555123456",
    searchType: "msisdn",
    riskLevel: "Low",
    keyFeatures: [
      "Consistent device usage",
      "Normal call patterns",
      "Regular payment behavior",
      "Stable location patterns"
    ],
    fraudPatterns: [],
    showcasePoints: [
      "Baseline comparison",
      "Normal behavior modeling",
      "Risk score calibration",
      "False positive prevention"
    ]
  },
  {
    id: "roaming-abuse",
    name: "Roaming & Location Fraud",
    description: "Subscriber with impossible travel patterns and roaming abuse",
    searchQuery: "310150987654321",
    searchType: "imsi",
    riskLevel: "Medium",
    keyFeatures: [
      "Impossible travel detection",
      "Roaming anomalies",
      "Location spoofing indicators",
      "High mobility patterns"
    ],
    fraudPatterns: [
      "Location spoofing",
      "Roaming fraud",
      "Impossible travel",
      "Geographic anomalies"
    ],
    showcasePoints: [
      "Geospatial intelligence",
      "Travel pattern analysis",
      "Location verification",
      "Roaming validation"
    ]
  }
]

export function getScenarioByQuery(searchQuery: string, searchType: "msisdn" | "imsi"): DemoScenario | undefined {
  return demoScenarios.find(scenario => 
    scenario.searchQuery === searchQuery && scenario.searchType === searchType
  )
}

export function getRandomScenario(): DemoScenario {
  const randomIndex = Math.floor(Math.random() * demoScenarios.length)
  return demoScenarios[randomIndex]
}

// Demo tips and insights for each scenario
export const scenarioInsights = {
  "high-risk-international": [
    "Notice the AI confidence score and multiple risk factors",
    "Observe IMEI highlighting across different activity cards",
    "Check the international call destinations and risk levels",
    "Review the device switching timeline and patterns"
  ],
  "bulk-sms-operation": [
    "Examine the SMS volume and bulk detection algorithms",
    "Look at data usage patterns indicating tethering",
    "Notice the commercial usage indicators",
    "Check the hourly distribution of activities"
  ],
  "device-fraud-ring": [
    "Analyze the multiple IMEI associations",
    "Review location clustering and movement patterns",
    "Examine dealer association and activation patterns",
    "Notice coordinated timing of activities"
  ],
  "normal-user-baseline": [
    "Compare risk scores with other scenarios",
    "Notice the consistent device and location patterns",
    "Observe normal communication behaviors",
    "Use as baseline for fraud detection calibration"
  ],
  "roaming-abuse": [
    "Check impossible travel calculations",
    "Review roaming patterns and costs",
    "Examine location verification failures",
    "Notice geographic anomaly detection"
  ]
}

// Interactive demo prompts
export const demoPrompts = {
  search: [
    "Try searching for different subscriber types:",
    "• +1234567890 (High-risk international fraud)",
    "• +1987654321 (Bulk SMS operation)",
    "• 310150123456789 (Device fraud ring)",
    "• +1555123456 (Normal user baseline)"
  ],
  analysis: [
    "Watch the AI analysis progress:",
    "• Multiple ML models running in parallel",
    "• Real-time confidence scoring",
    "• Risk factor identification",
    "• Automated recommendations"
  ],
  interaction: [
    "Try these interactive features:",
    "• Click any IMEI to highlight across cards",
    "• Hover over chart elements for details",
    "• Use date range filters",
    "• Export professional reports"
  ],
  exploration: [
    "Explore different dashboard sections:",
    "• Real-time monitoring with live alerts",
    "• Interactive maps with fraud hotspots",
    "• Case management workflow",
    "• Advanced filtering options"
  ]
}

export function getScenarioPrompts(scenarioId: string): string[] {
  return scenarioInsights[scenarioId as keyof typeof scenarioInsights] || []
}
