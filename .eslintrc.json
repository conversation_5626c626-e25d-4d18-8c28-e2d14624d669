{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "eqeqeq": "error", "curly": "error", "no-duplicate-imports": "error", "no-unused-expressions": "error", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react-hooks/exhaustive-deps": "warn"}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.*"], "rules": {"@typescript-eslint/no-explicit-any": "off", "no-console": "off"}}]}