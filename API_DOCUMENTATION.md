# 🔌 **FraudGuard 360° API Documentation**

## 📋 **Overview**

The FraudGuard 360° platform provides a comprehensive REST API with enterprise-grade features including authentication, real-time fraud detection, CDR processing, streaming analytics, and comprehensive monitoring capabilities.

**Base URL**: `http://localhost:3000/api` (Development)
**Production URL**: `https://your-domain.com/api`

**API Version**: 1.0.0
**Authentication**: JWT Bearer Token
**Rate Limiting**: 100 requests per 15 minutes (configurable)

---

## 🔐 **Authentication**

### **POST** `/api/auth/login`
Authenticate user and create session.

**Request Body:**
```json
{
  "username": "fraud.analyst",
  "password": "demo123"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "1",
    "username": "fraud.analyst",
    "email": "<EMAIL>",
    "role": "fraud_analyst",
    "permissions": ["view_dashboard", "export_reports", "manage_cases"]
  },
  "token": "base64_encoded_token"
}
```

### **GET** `/api/auth/login`
Get available demo credentials.

**Response:**
```json
{
  "demo_credentials": [
    {
      "username": "fraud.analyst",
      "password": "demo123",
      "role": "Fraud Analyst",
      "description": "Full access to fraud detection features"
    }
  ]
}
```

### **GET** `/api/auth/me`
Get current user information.

**Response:**
```json
{
  "user": {
    "id": "1",
    "username": "fraud.analyst",
    "role": "fraud_analyst"
  },
  "authenticated": true
}
```

### **POST** `/api/auth/logout`
Logout user and clear session.

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## 🚨 **Fraud Detection**

### **GET** `/api/fraud/detect/{id}`
Perform real-time fraud detection analysis for a subscriber.

**Parameters:**
- `id` (path): Subscriber identifier (MSISDN or IMSI)
- `refresh` (query, optional): Force fresh analysis (true/false)

**Example Request:**
```bash
GET /api/fraud/detect/+1555123456?refresh=true
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriber_id": "+1555123456",
    "fraud_analysis": {
      "risk_score": 85,
      "risk_level": "HIGH",
      "confidence": 0.92,
      "analysis_timestamp": "2024-01-15T10:30:00Z",
      "processing_time_ms": 1250
    },
    "alerts": [
      {
        "type": "VELOCITY_FRAUD",
        "severity": "HIGH",
        "title": "Unusual Call Volume Detected",
        "description": "Subscriber has made an unusually high number of calls",
        "risk_score": 85,
        "confidence": 0.92,
        "evidence": {
          "callsLastHour": 75,
          "normalAverage": 12,
          "timeWindow": "1 hour"
        }
      }
    ],
    "recommendations": [
      "IMMEDIATE ACTION: Suspend account pending investigation",
      "Contact subscriber to verify recent activity"
    ]
  }
}
```

---

## 📁 **CDR Processing**

### **GET** `/api/cdr/process`
Process CDR (Call Detail Record) files or generate sample data.

**Parameters:**
- `action` (query): "process" or "generate_sample"
- `format` (query): "csv", "xml", or "json" (for sample generation)
- `records` (query): Number of records to generate (default: 100)

**Example Requests:**
```bash
# Process all CDR files
GET /api/cdr/process?action=process

# Generate sample CDR file
GET /api/cdr/process?action=generate_sample&format=csv&records=1000
```

**Response (Process):**
```json
{
  "success": true,
  "data": {
    "action": "process",
    "message": "CDR processing completed",
    "summary": {
      "files_processed": 3,
      "total_records": 15000,
      "successful_records": 14850,
      "error_records": 150,
      "success_rate": "99.00%"
    },
    "file_results": [
      {
        "file_name": "cdr_20240115.csv",
        "format": "csv",
        "total_records": 5000,
        "successful_records": 4950,
        "error_records": 50,
        "processing_time_ms": 2500
      }
    ]
  }
}
```

---

## 📡 **Real-Time Streaming**

### **GET** `/api/streaming/events`
Control and monitor real-time event streaming.

**Parameters:**
- `action` (query): "status", "start", "stop", or "latest"
- `type` (query): Event type filter for latest events
- `limit` (query): Number of events to return (default: 50)

**Example Requests:**
```bash
# Get streaming status
GET /api/streaming/events?action=status

# Start streaming
GET /api/streaming/events?action=start

# Get latest fraud alerts
GET /api/streaming/events?action=latest&type=FRAUD_ALERT&limit=10
```

### **GET** `/api/streaming/sse`
Server-Sent Events endpoint for real-time updates.

**Parameters:**
- `types` (query): Comma-separated event types to subscribe to
- `client_id` (query): Unique client identifier

**Example:**
```javascript
const eventSource = new EventSource('/api/streaming/sse?types=FRAUD_ALERT,NEW_CALL');

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Real-time event:', data);
};
```

---

## 📊 **Monitoring & Performance**

### **GET** `/api/monitoring/performance`
Get system performance metrics and health information.

**Parameters:**
- `health` (query): Include health check data (true/false)
- `details` (query): Include detailed metrics (true/false)

**Response:**
```json
{
  "success": true,
  "data": {
    "timestamp": "2024-01-15T10:30:00Z",
    "monitoring_enabled": true,
    "basic_metrics": {
      "uptime": 86400,
      "memory": {
        "used": 512,
        "total": 1024,
        "usage_percent": 50
      },
      "node_version": "v18.17.0",
      "platform": "linux"
    },
    "health": {
      "status": "healthy",
      "issues": [],
      "summary": {
        "apiCalls": {},
        "dbQueries": {},
        "memoryUsage": {
          "current": 50,
          "average": 45,
          "max": 75
        }
      }
    }
  }
}
```

### **GET** `/api/health`
Comprehensive system health check.

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "overall_status": "healthy",
  "services": {
    "application": {
      "status": "healthy",
      "uptime": 86400,
      "memory_usage": 50,
      "node_version": "v18.17.0"
    },
    "database": {
      "status": "healthy",
      "details": {
        "connected": true,
        "response_time_ms": 15
      }
    },
    "cache": {
      "status": "healthy",
      "details": {
        "redis_connected": true,
        "memory_cache_size": 1024
      }
    }
  },
  "environment": {
    "node_env": "production",
    "enable_real_data": true,
    "enable_cache": true,
    "enable_performance_monitoring": true
  },
  "response_time_ms": 45
}
```

---

## 👤 **Subscriber Data**

### **GET** `/api/subscribers/{id}`
Fetch comprehensive subscriber data with AI analysis.

**Parameters:**
- `id` (path): Subscriber identifier (MSISDN or IMSI)
- `type` (query): Search type - "msisdn" or "imsi"
- `startDate` (query, optional): Analysis start date (ISO string)
- `endDate` (query, optional): Analysis end date (ISO string)

**Example Request:**
```
GET /api/subscribers/+**********?type=msisdn&startDate=2024-01-01T00:00:00Z&endDate=2024-01-31T23:59:59Z
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "msisdn": "+**********",
      "imsi": "310150123456789",
      "currentImei": "356938035643809",
      "status": "Active",
      "activationDate": "2023-03-15T10:30:00Z",
      "lastActivity": "2024-01-15T14:30:00Z",
      "currentLocation": {
        "cellSite": "NYC_001_A",
        "lac": "1234",
        "coordinates": { "lat": 40.7128, "lng": -74.0060 },
        "address": "Manhattan, New York, NY"
      }
    },
    "aiAnalysis": {
      "overallRiskScore": 75,
      "behavioralScore": 65,
      "networkScore": 80,
      "deviceScore": 70,
      "velocityScore": 85,
      "confidence": 87,
      "riskFactors": ["Bulk SMS Activity", "International Call Pattern"],
      "recommendations": ["Enhanced monitoring required", "Manual review of recent activities"]
    }
  },
  "metadata": {
    "searchQuery": "+**********",
    "searchType": "msisdn",
    "generatedAt": "2024-01-15T15:00:00Z",
    "processingTime": "1.5s",
    "apiVersion": "1.0.0"
  }
}
```

### **POST** `/api/subscribers/{id}`
Update subscriber data or add notes.

**Request Body:**
```json
{
  "notes": "Suspicious activity detected",
  "riskLevel": "high",
  "investigationStatus": "active"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Subscriber +********** updated successfully",
  "updatedFields": ["notes", "riskLevel", "investigationStatus"],
  "timestamp": "2024-01-15T15:00:00Z"
}
```

---

## 📊 **Analytics**

### **GET** `/api/analytics/real-time`
Fetch real-time fraud detection analytics and system metrics.

**Response:**
```json
{
  "success": true,
  "data": {
    "timestamp": "2024-01-15T15:00:00Z",
    "system_status": {
      "fraud_detection_rate": 87,
      "active_investigations": 156,
      "risk_alerts": 8,
      "network_health": 94,
      "processing_speed": 2150,
      "false_positive_rate": 3
    },
    "fraud_trends": [
      {
        "hour": 0,
        "fraud_attempts": 15,
        "blocked": 35,
        "investigated": 8
      }
    ],
    "risk_distribution": [
      { "name": "Low Risk", "value": 65, "color": "#10B981" },
      { "name": "Medium Risk", "value": 25, "color": "#F59E0B" },
      { "name": "High Risk", "value": 8, "color": "#EF4444" },
      { "name": "Critical", "value": 2, "color": "#DC2626" }
    ],
    "performance_metrics": {
      "cpu_usage": 45,
      "memory_usage": 62,
      "database_load": 38,
      "network_throughput": 78
    }
  }
}
```

---

## 🏥 **System Health**

### **GET** `/api/health`
Get system health status and metrics.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T15:00:00Z",
  "version": "1.0.0",
  "environment": "development",
  "services": {
    "database": "connected",
    "redis": "connected",
    "ai_models": "operational",
    "fraud_engine": "active"
  },
  "metrics": {
    "uptime": 86400,
    "memory_usage": 65,
    "cpu_usage": 45,
    "active_sessions": 87,
    "requests_per_minute": 450
  }
}
```

---

## 🔒 **Security Features**

### **Authentication**
- HTTP-only cookies for session management
- Base64 encoded JWT-like tokens (demo implementation)
- Role-based access control (RBAC)
- Permission-based authorization

### **Authorization Levels**
- **Admin**: Full system access with administrative privileges
- **Fraud Analyst**: Complete fraud detection and investigation features
- **Demo User**: Limited read-only access for demonstration

### **Permissions**
- `view_dashboard`: Access to main dashboard and analytics
- `export_reports`: Ability to export data and generate reports
- `manage_cases`: Case management and investigation tools
- `admin_panel`: Administrative functions and system settings

---

## 🚀 **Usage Examples**

### **JavaScript/TypeScript**
```typescript
// Using the custom useApi hook
import { useSubscriberApi } from '@/hooks/use-api'

const { fetchSubscriber, isLoading, error } = useSubscriberApi()

const handleSearch = async () => {
  const result = await fetchSubscriber('+**********', 'msisdn')
  if (result.success) {
    console.log('Subscriber data:', result.data)
  }
}
```

### **cURL Examples**
```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"fraud.analyst","password":"demo123"}'

# Fetch subscriber data
curl -X GET "http://localhost:3000/api/subscribers/+**********?type=msisdn" \
  -H "Cookie: auth-token=your_token_here"

# Get real-time analytics
curl -X GET http://localhost:3000/api/analytics/real-time \
  -H "Cookie: auth-token=your_token_here"

# Check system health
curl -X GET http://localhost:3000/api/health
```

---

## 📝 **Error Handling**

### **Standard Error Response**
```json
{
  "success": false,
  "error": "Error message description",
  "details": "Additional error details (optional)"
}
```

### **HTTP Status Codes**
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error

---

## 🔧 **Development Notes**

### **Mock Implementation**
This API is designed for demonstration purposes and uses:
- In-memory user database
- Simulated data generation
- Mock authentication tokens
- Artificial processing delays for realism

### **Production Considerations**
For production deployment, implement:
- Real database connections (PostgreSQL, MongoDB)
- Proper JWT token handling with secrets
- Password hashing (bcrypt, argon2)
- Rate limiting and request validation
- Comprehensive error logging
- API versioning and documentation

---

This API documentation demonstrates full-stack development capabilities including RESTful design, authentication, authorization, data processing, and real-time analytics - perfect for showcasing technical skills in an intern project portfolio.
