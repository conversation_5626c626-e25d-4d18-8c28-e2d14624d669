# 🔌 **FraudGuard 360° API Documentation**

## 📋 **Overview**

The FraudGuard 360° platform includes a comprehensive REST API that demonstrates full-stack development capabilities with authentication, data processing, and real-time analytics.

---

## 🔐 **Authentication**

### **POST** `/api/auth/login`
Authenticate user and create session.

**Request Body:**
```json
{
  "username": "fraud.analyst",
  "password": "demo123"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "1",
    "username": "fraud.analyst",
    "email": "<EMAIL>",
    "role": "fraud_analyst",
    "permissions": ["view_dashboard", "export_reports", "manage_cases"]
  },
  "token": "base64_encoded_token"
}
```

### **GET** `/api/auth/login`
Get available demo credentials.

**Response:**
```json
{
  "demo_credentials": [
    {
      "username": "fraud.analyst",
      "password": "demo123",
      "role": "Fraud Analyst",
      "description": "Full access to fraud detection features"
    }
  ]
}
```

### **GET** `/api/auth/me`
Get current user information.

**Response:**
```json
{
  "user": {
    "id": "1",
    "username": "fraud.analyst",
    "role": "fraud_analyst"
  },
  "authenticated": true
}
```

### **POST** `/api/auth/logout`
Logout user and clear session.

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## 👤 **Subscriber Data**

### **GET** `/api/subscribers/{id}`
Fetch comprehensive subscriber data with AI analysis.

**Parameters:**
- `id` (path): Subscriber identifier (MSISDN or IMSI)
- `type` (query): Search type - "msisdn" or "imsi"
- `startDate` (query, optional): Analysis start date (ISO string)
- `endDate` (query, optional): Analysis end date (ISO string)

**Example Request:**
```
GET /api/subscribers/+**********?type=msisdn&startDate=2024-01-01T00:00:00Z&endDate=2024-01-31T23:59:59Z
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "msisdn": "+**********",
      "imsi": "310150123456789",
      "currentImei": "356938035643809",
      "status": "Active",
      "activationDate": "2023-03-15T10:30:00Z",
      "lastActivity": "2024-01-15T14:30:00Z",
      "currentLocation": {
        "cellSite": "NYC_001_A",
        "lac": "1234",
        "coordinates": { "lat": 40.7128, "lng": -74.0060 },
        "address": "Manhattan, New York, NY"
      }
    },
    "aiAnalysis": {
      "overallRiskScore": 75,
      "behavioralScore": 65,
      "networkScore": 80,
      "deviceScore": 70,
      "velocityScore": 85,
      "confidence": 87,
      "riskFactors": ["Bulk SMS Activity", "International Call Pattern"],
      "recommendations": ["Enhanced monitoring required", "Manual review of recent activities"]
    }
  },
  "metadata": {
    "searchQuery": "+**********",
    "searchType": "msisdn",
    "generatedAt": "2024-01-15T15:00:00Z",
    "processingTime": "1.5s",
    "apiVersion": "1.0.0"
  }
}
```

### **POST** `/api/subscribers/{id}`
Update subscriber data or add notes.

**Request Body:**
```json
{
  "notes": "Suspicious activity detected",
  "riskLevel": "high",
  "investigationStatus": "active"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Subscriber +********** updated successfully",
  "updatedFields": ["notes", "riskLevel", "investigationStatus"],
  "timestamp": "2024-01-15T15:00:00Z"
}
```

---

## 📊 **Analytics**

### **GET** `/api/analytics/real-time`
Fetch real-time fraud detection analytics and system metrics.

**Response:**
```json
{
  "success": true,
  "data": {
    "timestamp": "2024-01-15T15:00:00Z",
    "system_status": {
      "fraud_detection_rate": 87,
      "active_investigations": 156,
      "risk_alerts": 8,
      "network_health": 94,
      "processing_speed": 2150,
      "false_positive_rate": 3
    },
    "fraud_trends": [
      {
        "hour": 0,
        "fraud_attempts": 15,
        "blocked": 35,
        "investigated": 8
      }
    ],
    "risk_distribution": [
      { "name": "Low Risk", "value": 65, "color": "#10B981" },
      { "name": "Medium Risk", "value": 25, "color": "#F59E0B" },
      { "name": "High Risk", "value": 8, "color": "#EF4444" },
      { "name": "Critical", "value": 2, "color": "#DC2626" }
    ],
    "performance_metrics": {
      "cpu_usage": 45,
      "memory_usage": 62,
      "database_load": 38,
      "network_throughput": 78
    }
  }
}
```

---

## 🏥 **System Health**

### **GET** `/api/health`
Get system health status and metrics.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T15:00:00Z",
  "version": "1.0.0",
  "environment": "development",
  "services": {
    "database": "connected",
    "redis": "connected",
    "ai_models": "operational",
    "fraud_engine": "active"
  },
  "metrics": {
    "uptime": 86400,
    "memory_usage": 65,
    "cpu_usage": 45,
    "active_sessions": 87,
    "requests_per_minute": 450
  }
}
```

---

## 🔒 **Security Features**

### **Authentication**
- HTTP-only cookies for session management
- Base64 encoded JWT-like tokens (demo implementation)
- Role-based access control (RBAC)
- Permission-based authorization

### **Authorization Levels**
- **Admin**: Full system access with administrative privileges
- **Fraud Analyst**: Complete fraud detection and investigation features
- **Demo User**: Limited read-only access for demonstration

### **Permissions**
- `view_dashboard`: Access to main dashboard and analytics
- `export_reports`: Ability to export data and generate reports
- `manage_cases`: Case management and investigation tools
- `admin_panel`: Administrative functions and system settings

---

## 🚀 **Usage Examples**

### **JavaScript/TypeScript**
```typescript
// Using the custom useApi hook
import { useSubscriberApi } from '@/hooks/use-api'

const { fetchSubscriber, isLoading, error } = useSubscriberApi()

const handleSearch = async () => {
  const result = await fetchSubscriber('+**********', 'msisdn')
  if (result.success) {
    console.log('Subscriber data:', result.data)
  }
}
```

### **cURL Examples**
```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"fraud.analyst","password":"demo123"}'

# Fetch subscriber data
curl -X GET "http://localhost:3000/api/subscribers/+**********?type=msisdn" \
  -H "Cookie: auth-token=your_token_here"

# Get real-time analytics
curl -X GET http://localhost:3000/api/analytics/real-time \
  -H "Cookie: auth-token=your_token_here"

# Check system health
curl -X GET http://localhost:3000/api/health
```

---

## 📝 **Error Handling**

### **Standard Error Response**
```json
{
  "success": false,
  "error": "Error message description",
  "details": "Additional error details (optional)"
}
```

### **HTTP Status Codes**
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error

---

## 🔧 **Development Notes**

### **Mock Implementation**
This API is designed for demonstration purposes and uses:
- In-memory user database
- Simulated data generation
- Mock authentication tokens
- Artificial processing delays for realism

### **Production Considerations**
For production deployment, implement:
- Real database connections (PostgreSQL, MongoDB)
- Proper JWT token handling with secrets
- Password hashing (bcrypt, argon2)
- Rate limiting and request validation
- Comprehensive error logging
- API versioning and documentation

---

This API documentation demonstrates full-stack development capabilities including RESTful design, authentication, authorization, data processing, and real-time analytics - perfect for showcasing technical skills in an intern project portfolio.
