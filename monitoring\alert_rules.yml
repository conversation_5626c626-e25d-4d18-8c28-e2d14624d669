groups:
- name: fraudguard.rules
  rules:
  # Application Health Alerts
  - alert: FraudGuardAppDown
    expr: up{job="fraudguard-app"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "FraudGuard application is down"
      description: "FraudGuard application has been down for more than 1 minute."

  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second."

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }} seconds."

  # Database Alerts
  - alert: DatabaseDown
    expr: up{job="postgres-exporter"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "PostgreSQL database is down"
      description: "PostgreSQL database has been down for more than 1 minute."

  - alert: HighDatabaseConnections
    expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High database connection usage"
      description: "Database connection usage is {{ $value | humanizePercentage }}."

  - alert: SlowDatabaseQueries
    expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Slow database queries detected"
      description: "Database query efficiency is low."

  # Redis Alerts
  - alert: RedisDown
    expr: up{job="redis-exporter"} == 0
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "Redis cache is down"
      description: "Redis cache has been down for more than 1 minute."

  - alert: HighRedisMemoryUsage
    expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High Redis memory usage"
      description: "Redis memory usage is {{ $value | humanizePercentage }}."

  # System Resource Alerts
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

  - alert: LowDiskSpace
    expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low disk space"
      description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

  # Fraud Detection Specific Alerts
  - alert: HighFraudAlertVolume
    expr: rate(fraud_alerts_total[5m]) > 10
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High volume of fraud alerts"
      description: "Fraud alert rate is {{ $value }} alerts per second."

  - alert: CriticalFraudAlert
    expr: fraud_alerts_total{severity="CRITICAL"} > 0
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: "Critical fraud alert detected"
      description: "A critical fraud alert has been triggered."

  - alert: CDRProcessingFailure
    expr: rate(cdr_processing_errors_total[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CDR processing failures detected"
      description: "CDR processing error rate is {{ $value }} errors per second."

  # Kubernetes Alerts
  - alert: KubernetesPodCrashLooping
    expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Pod is crash looping"
      description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is crash looping."

  - alert: KubernetesPodNotReady
    expr: kube_pod_status_ready{condition="false"} == 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Pod not ready"
      description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} has been not ready for more than 5 minutes."
