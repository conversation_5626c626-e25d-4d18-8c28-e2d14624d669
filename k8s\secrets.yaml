apiVersion: v1
kind: Secret
metadata:
  name: fraudguard-secrets
  namespace: fraudguard
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  DATABASE_URL: ********************************************************************************************************
  REDIS_URL: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=
  NEXTAUTH_SECRET: cHJvZHVjdGlvbi1zZWNyZXQta2V5LWNoYW5nZS10aGlzLWluLXByb2R1Y3Rpb24=
  ENCRYPTION_KEY: ""
  JWT_SECRET: cHJvZHVjdGlvbi1qd3Qtc2VjcmV0LWtleS1jaGFuZ2UtdGhpcw==
---
apiVersion: v1
kind: Secret
metadata:
  name: fraudguard-secrets-staging
  namespace: fraudguard-staging
type: Opaque
data:
  # Base64 encoded values for staging
  DATABASE_URL: ****************************************************************************************************************
  REDIS_URL: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=
  NEXTAUTH_SECRET: c3RhZ2luZy1zZWNyZXQta2V5LWNoYW5nZS10aGlz
  ENCRYPTION_KEY: ""
  JWT_SECRET: c3RhZ2luZy1qd3Qtc2VjcmV0LWtleS1jaGFuZ2UtdGhpcw==
