apiVersion: v1
kind: ConfigMap
metadata:
  name: fraudguard-config
  namespace: fraudguard
data:
  NODE_ENV: "production"
  NEXT_TELEMETRY_DISABLED: "1"
  ENABLE_REAL_DATA: "true"
  ENABLE_REDIS_CACHE: "true"
  ENABLE_PERFORMANCE_MONITORING: "true"
  ENABLE_REAL_TIME_STREAMING: "true"
  ENABLE_CDR_PROCESSING: "true"
  ENABLE_FRAUD_DETECTION: "true"
  LOG_LEVEL: "info"
  ENABLE_FILE_LOGGING: "true"
  ENABLE_CONSOLE_LOGGING: "true"
  CDR_BATCH_SIZE: "1000"
  CDR_MAX_FILE_SIZE: "100"
  VELOCITY_THRESHOLD: "50"
  COST_THRESHOLD: "100"
  RISK_SCORE_THRESHOLD: "75"
  CONFIDENCE_THRESHOLD: "0.8"
  STREAMING_EVENT_INTERVAL: "5000"
  STREAMING_BATCH_SIZE: "10"
  RATE_LIMIT_MAX_REQUESTS: "100"
  RATE_LIMIT_WINDOW_MS: "900000"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fraudguard-config-staging
  namespace: fraudguard-staging
data:
  NODE_ENV: "staging"
  NEXT_TELEMETRY_DISABLED: "1"
  ENABLE_REAL_DATA: "true"
  ENABLE_REDIS_CACHE: "true"
  ENABLE_PERFORMANCE_MONITORING: "true"
  ENABLE_REAL_TIME_STREAMING: "true"
  ENABLE_CDR_PROCESSING: "true"
  ENABLE_FRAUD_DETECTION: "true"
  LOG_LEVEL: "debug"
  ENABLE_FILE_LOGGING: "true"
  ENABLE_CONSOLE_LOGGING: "true"
  CDR_BATCH_SIZE: "500"
  CDR_MAX_FILE_SIZE: "50"
  VELOCITY_THRESHOLD: "30"
  COST_THRESHOLD: "50"
  RISK_SCORE_THRESHOLD: "60"
  CONFIDENCE_THRESHOLD: "0.7"
  STREAMING_EVENT_INTERVAL: "3000"
  STREAMING_BATCH_SIZE: "5"
  RATE_LIMIT_MAX_REQUESTS: "200"
  RATE_LIMIT_WINDOW_MS: "900000"
