import { NextResponse } from 'next/server'

export async function GET() {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {
      database: 'connected',
      redis: 'connected',
      ai_models: 'operational',
      fraud_engine: 'active'
    },
    metrics: {
      uptime: Math.floor(Math.random() * 86400), // Random uptime in seconds
      memory_usage: Math.floor(Math.random() * 80) + 20, // 20-100%
      cpu_usage: Math.floor(Math.random() * 60) + 10, // 10-70%
      active_sessions: Math.floor(Math.random() * 100) + 50,
      requests_per_minute: Math.floor(Math.random() * 1000) + 200
    }
  }

  return NextResponse.json(healthData)
}
