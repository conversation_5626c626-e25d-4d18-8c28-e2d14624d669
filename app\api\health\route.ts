import { NextResponse } from 'next/server'
import { checkDatabaseHealth, shouldUseRealData } from '@/lib/database'

export async function GET() {
  // Check database health if using real data
  const dbHealth = shouldUseRealData() ? await checkDatabaseHealth() : {
    status: 'mock',
    timestamp: new Date().toISOString(),
    note: 'Using mock data for demo'
  }

  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {
      database: dbHealth.status,
      redis: shouldUseRealData() ? 'checking...' : 'mock',
      ai_models: 'operational',
      fraud_engine: 'active',
      data_mode: shouldUseRealData() ? 'real_database' : 'mock_demo'
    },
    database_health: dbHealth,
    metrics: {
      uptime: Math.floor(Math.random() * 86400), // Random uptime in seconds
      memory_usage: Math.floor(Math.random() * 80) + 20, // 20-100%
      cpu_usage: Math.floor(Math.random() * 60) + 10, // 10-70%
      active_sessions: Math.floor(Math.random() * 100) + 50,
      requests_per_minute: Math.floor(Math.random() * 1000) + 200
    }
  }

  return NextResponse.json(healthData)
}
