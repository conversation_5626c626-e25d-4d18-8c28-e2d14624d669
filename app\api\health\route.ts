import { NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security'
import { performHealthCheck } from '@/lib/init'
import { createTimer } from '@/lib/logger'

async function healthHandler() {
  const timer = createTimer()

  try {
    // Perform comprehensive health check
    const healthData = await performHealthCheck()

    // Add response time
    const responseTime = timer.end()
    healthData.response_time_ms = responseTime

    return NextResponse.json(healthData)
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {
      database: dbHealth.status,
      redis: shouldUseRealData() ? 'checking...' : 'mock',
      ai_models: 'operational',
      fraud_engine: 'active',
      data_mode: shouldUseRealData() ? 'real_database' : 'mock_demo'
    },
    database_health: dbHealth,
    metrics: {
      uptime: Math.floor(uptimeSeconds),
      memory_usage: memoryUsagePercent,
      memory_details: {
        heap_used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        heap_total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
      },
      cpu_usage: Math.floor(Math.random() * 60) + 10, // Simulated for demo
      active_sessions: Math.floor(Math.random() * 100) + 50, // Simulated
      requests_per_minute: Math.floor(Math.random() * 1000) + 200, // Simulated
      response_time: timer.end(),
    },
    performance: {
      node_version: process.version,
      platform: process.platform,
      arch: process.arch,
    }
  }

  // Log system health
  const overallStatus = dbHealth.status === 'healthy' || dbHealth.status === 'mock' ? 'healthy' : 'degraded'
  logSystemHealth('application', overallStatus, healthData.metrics)

  const duration = timer.end()

  return NextResponse.json(healthData)

  } catch (error) {
    const duration = timer.end()

    logSystemHealth('application', 'unhealthy', {
      error: error instanceof Error ? error.message : 'Unknown error',
      duration
    })

    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Apply security middleware
export const GET = withSecurity(healthHandler)
