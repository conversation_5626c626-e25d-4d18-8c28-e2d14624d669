version: '3.8'

services:
  # Main Application
  fraudguard-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fraudguard-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - DATABASE_URL=*********************************************************/fraudguard
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_SECRET=production-secret-key-change-this
      - ENABLE_REAL_DATA=true
      - ENABLE_REDIS_CACHE=true
      - ENABLE_PERFORMANCE_MONITORING=true
      - ENABLE_REAL_TIME_STREAMING=true
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - cdr_data:/app/data
      - app_logs:/app/logs
    networks:
      - fraudguard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and real-time features
  redis:
    image: redis:7-alpine
    container_name: fraudguard-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fraudguard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 30s
    command: redis-server --appendonly yes

  # PostgreSQL for data storage
  postgres:
    image: postgres:15-alpine
    container_name: fraudguard-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=fraudguard
      - POSTGRES_USER=fraudguard
      - POSTGRES_PASSWORD=fraudguard_password
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - fraudguard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fraudguard -d fraudguard"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - fraudguard-app
    networks:
      - fraudguard-network
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - fraudguard-network
    restart: unless-stopped

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - fraudguard-network
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  cdr_data:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  fraudguard-network:
    driver: bridge
