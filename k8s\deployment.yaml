apiVersion: apps/v1
kind: Deployment
metadata:
  name: fraudguard-app
  labels:
    app: fraudguard
    component: frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fraudguard
      component: frontend
  template:
    metadata:
      labels:
        app: fraudguard
        component: frontend
    spec:
      containers:
      - name: fraudguard-app
        image: ghcr.io/your-username/fraudguard-360:latest
        ports:
        - containerPort: 3000
        env:
        - name: PORT
          value: "3000"
        - name: HOSTNAME
          value: "0.0.0.0"
        envFrom:
        - configMapRef:
            name: fraudguard-config
        - secretRef:
            name: fraudguard-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: cdr-data
          mountPath: /app/data
        - name: app-logs
          mountPath: /app/logs
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      volumes:
      - name: cdr-data
        persistentVolumeClaim:
          claimName: fraudguard-cdr-pvc
      - name: app-logs
        persistentVolumeClaim:
          claimName: fraudguard-logs-pvc
      securityContext:
        fsGroup: 1001
---
apiVersion: v1
kind: Service
metadata:
  name: fraudguard-service
  labels:
    app: fraudguard
spec:
  selector:
    app: fraudguard
    component: frontend
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fraudguard-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - fraudguard.yourdomain.com
    secretName: fraudguard-tls
  rules:
  - host: fraudguard.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fraudguard-service
            port:
              number: 80
---
apiVersion: v1
kind: Secret
metadata:
  name: fraudguard-secrets
type: Opaque
data:
  # Base64 encoded values
  database-url: cG9zdGdyZXNxbDovL3VzZXI6cGFzc3dvcmRAaG9zdDpwb3J0L2RiCg==
  redis-url: cmVkaXM6Ly9ob3N0OnBvcnQK
  nextauth-secret: eW91ci1uZXh0YXV0aC1zZWNyZXQK
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fraudguard-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fraudguard-app
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
