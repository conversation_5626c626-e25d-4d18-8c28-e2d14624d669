apiVersion: apps/v1
kind: Deployment
metadata:
  name: fraudguard-app
  labels:
    app: fraudguard
    component: frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fraudguard
      component: frontend
  template:
    metadata:
      labels:
        app: fraudguard
        component: frontend
    spec:
      containers:
      - name: fraudguard-app
        image: ghcr.io/your-username/fraudguard-360:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_TELEMETRY_DISABLED
          value: "1"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: fraudguard-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: fraudguard-secrets
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: fraudguard-service
  labels:
    app: fraudguard
spec:
  selector:
    app: fraudguard
    component: frontend
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fraudguard-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - fraudguard.yourdomain.com
    secretName: fraudguard-tls
  rules:
  - host: fraudguard.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fraudguard-service
            port:
              number: 80
---
apiVersion: v1
kind: Secret
metadata:
  name: fraudguard-secrets
type: Opaque
data:
  # Base64 encoded values
  database-url: cG9zdGdyZXNxbDovL3VzZXI6cGFzc3dvcmRAaG9zdDpwb3J0L2RiCg==
  redis-url: cmVkaXM6Ly9ob3N0OnBvcnQK
  nextauth-secret: eW91ci1uZXh0YXV0aC1zZWNyZXQK
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fraudguard-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fraudguard-app
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
